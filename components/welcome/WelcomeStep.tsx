import React from 'react';
import { View, Text, Image, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import { StyledText } from '../ui/StyledComponents'; // Use custom styled components

interface WelcomeStepProps {
  imageSource: any; // Adjust type as per your image source (e.g., ImageSourcePropType)
  onGetStarted?: () => void;
  onLogin?: () => void;
  onRegister?: () => void;
  showDualButtons?: boolean;
}

const { width } = Dimensions.get('window');

const WelcomeStep: React.FC<WelcomeStepProps> = ({
  imageSource,
  onGetStarted,
  onLogin,
  onRegister,
  showDualButtons = false,
}) => {
  return (
    <View style={styles.container}>
      <Image source={imageSource} style={styles.image} resizeMode="contain" />
      
      {showDualButtons ? (
        <View style={styles.dualButtonContainer}>
          <TouchableOpacity 
            onPress={onLogin} 
            style={[styles.buttonBase, styles.loginButton]}
          >
            <Text style={styles.loginButtonText}>Log in</Text>
          </TouchableOpacity>
          <TouchableOpacity 
            onPress={onRegister} 
            style={[styles.buttonBase, styles.registerButton]}
          >
            <Text style={styles.registerButtonText}>Register</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity 
          onPress={onGetStarted} 
          style={[styles.buttonBase, styles.singleButton]}
        >
          <Text style={styles.buttonText}>Get started</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    backgroundColor: '#FFFFFF', // Match asset background color (pure white)
  },
  image: {
    width: '85%', // Increase image size since no text
    height: 400, // Increase height for better prominence
    marginBottom: 60, // Increase margin to push buttons down
  },
  dualButtonContainer: {
    flexDirection: 'row',
    width: '80%',
    gap: 12,
    marginTop: 20,
  },
  buttonBase: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginTop: 20,
    backgroundColor: '#006494', // Use the secondary color directly
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50, // Ensure minimum touch target
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3, // Android shadow
  },
  singleButton: {
    width: '80%',
  },
  loginButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#006494',
    marginTop: 0,
  },
  registerButton: {
    flex: 1,
    backgroundColor: '#006494',
    marginTop: 0,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  loginButtonText: {
    color: '#006494',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  registerButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default WelcomeStep;
