import React from 'react';
import { Text, View, TextInput, TouchableOpacity, ScrollView, SafeAreaView, Image, StyleSheet, TextProps, ViewProps, TextInputProps, TouchableOpacityProps } from 'react-native';

// Define types for our styled components
type StyledTextProps = TextProps & {
  type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link';
  className?: string;
};

type StyledViewProps = ViewProps & {
  className?: string;
};

type StyledTextInputProps = TextInputProps & {
  label?: string;
  error?: string;
  className?: string;
};

type StyledTouchableOpacityProps = TouchableOpacityProps & {
  variant?: 'primary' | 'secondary' | 'outline' | 'link';
  className?: string;
};

// Styled Text component with different text types
export const StyledText: React.FC<StyledTextProps> = ({ style, type = 'default', className = '', ...rest }) => {
  // Define Tailwind classes based on text type
  let typeClasses = '';
  switch (type) {
    case 'default':
      typeClasses = 'text-base leading-6 text-body';
      break;
    case 'defaultSemiBold':
      typeClasses = 'text-base leading-6 font-semibold text-body';
      break;
    case 'title':
      typeClasses = 'text-3xl font-bold leading-8 text-primary';
      break;
    case 'subtitle':
      typeClasses = 'text-xl font-bold text-primary';
      break;
    case 'link':
      typeClasses = 'text-base leading-7 text-link';
      break;
  }
  
  return (
    <Text
      className={`${typeClasses} ${className}`}
      style={style}
      {...rest}
    />
  );
};

// Styled View component
export const StyledView: React.FC<StyledViewProps> = ({ className = '', ...rest }) => {
  return <View className={className} {...rest} />;
};

// Styled TextInput component with label and error handling
export const StyledTextInput: React.FC<StyledTextInputProps> = ({ 
  label, 
  error, 
  className = '', 
  ...rest 
}) => {
  return (
    <View className="mb-4">
      {label && (
        <Text className="text-sm font-medium mb-1 text-dark-gray">{label}</Text>
      )}
      <TextInput 
        className={`border border-gray-300 rounded-lg px-4 py-3 w-full bg-white min-h-[3rem] focus:border-gray-500 focus:outline-none focus:ring-0 ${error ? 'border-danger' : ''} ${className}`}
        placeholderTextColor="#9B9B9B"
        {...rest} 
      />
      {error && (
        <Text className="text-danger text-sm mt-1">{error}</Text>
      )}
    </View>
  );
};

// Styled TouchableOpacity component with different variants
export const StyledTouchableOpacity: React.FC<StyledTouchableOpacityProps> = ({ 
  variant = 'primary', 
  className = '', 
  disabled,
  ...rest 
}) => {
  // Define Tailwind classes based on button variant
  let variantClasses = '';
  switch (variant) {
    case 'primary':
      variantClasses = 'bg-primary rounded-lg py-3 items-center';
      break;
    case 'secondary':
      variantClasses = 'bg-secondary rounded-lg py-3 items-center';
      break;
    case 'outline':
      variantClasses = 'bg-white border border-dark-border rounded-lg py-3 items-center';
      break;
    case 'link':
      variantClasses = 'py-2';
      break;
  }
  
  return (
    <TouchableOpacity 
      className={`${variantClasses} ${disabled ? 'opacity-70' : ''} ${className}`}
      disabled={disabled}
      {...rest} 
    />
  );
};

// Re-export other components
export const StyledScrollView = ScrollView;
export const StyledSafeAreaView = SafeAreaView;
export const StyledImage = Image;
