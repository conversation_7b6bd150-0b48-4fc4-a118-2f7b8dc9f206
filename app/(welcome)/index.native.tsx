import React, { useRef, useState, useEffect } from 'react';
import { View, StyleSheet, SafeAreaView, Animated, Dimensions } from 'react-native';
import PagerView from 'react-native-pager-view';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import WelcomeStep from '../../components/welcome/WelcomeStep';

const { width: screenWidth } = Dimensions.get('window');

// Welcome steps data without text
const WELCOME_STEPS_DATA = [
  {
    id: '1',
    imageSource: require('../../assets/images/welcome-step-1.png'),
  },
  {
    id: '2',
    imageSource: require('../../assets/images/welcome-step-2.png'),
  },
  {
    id: '3',
    imageSource: require('../../assets/images/welcome-step-3.png'),
  },
];

export default function WelcomeScreenNative() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const pagerRef = useRef<PagerView>(null);
  
  // Create persistent animated values for each segment
  const progressAnimations = useRef(
    WELCOME_STEPS_DATA.map(() => new Animated.Value(0))
  ).current;
  
  const autoAdvanceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleGetStarted = async () => {
    // If not on the last step, advance to next page
    if (currentPage < WELCOME_STEPS_DATA.length - 1) {
      const nextPage = currentPage + 1;
      pagerRef.current?.setPage(nextPage);
      return;
    }
    
    // If on the last step, this shouldn't be called (but handle it just in case)
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleLogin = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleRegister = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/register');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/register');
    }
  };

  const onPageSelected = (e: any) => {
    const newPage = e.nativeEvent.position;
    setCurrentPage(newPage);
    
    // Reset current page animation and start it
    progressAnimations[newPage].setValue(0);
    startProgressAnimation(newPage);
  };

  const startProgressAnimation = (pageIndex: number) => {
    // Clear existing timer
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }

    // Don't auto-advance on the last page
    if (pageIndex >= WELCOME_STEPS_DATA.length - 1) {
      progressAnimations[pageIndex].setValue(1);
      return;
    }

    // Animate progress bar over 3 seconds
    Animated.timing(progressAnimations[pageIndex], {
      toValue: 1,
      duration: 3000,
      useNativeDriver: false,
    }).start();

    // Auto-advance to next page after 3 seconds
    autoAdvanceTimer.current = setTimeout(() => {
      const nextPage = pageIndex + 1;
      if (nextPage < WELCOME_STEPS_DATA.length) {
        pagerRef.current?.setPage(nextPage);
      }
    }, 3000);
  };

  useEffect(() => {
    // Start the first page animation
    startProgressAnimation(0);

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
    };
  }, []);

  const renderProgressBar = () => {
    return (
      <View style={styles.progressBarContainer}>
        {WELCOME_STEPS_DATA.map((_, index) => {
          let segmentWidth;
          
          if (index < currentPage) {
            // Completed segments - ensure they're set to 1
            progressAnimations[index].setValue(1);
            segmentWidth = progressAnimations[index];
          } else if (index === currentPage) {
            // Current segment with animation
            segmentWidth = progressAnimations[index];
          } else {
            // Future segments - ensure they're set to 0
            progressAnimations[index].setValue(0);
            segmentWidth = progressAnimations[index];
          }

          return (
            <View key={index} style={styles.progressSegmentContainer}>
              <View style={styles.progressSegmentBackground} />
              <Animated.View
                style={[
                  styles.progressSegmentFill,
                  {
                    width: segmentWidth.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    }),
                  },
                ]}
              />
            </View>
          );
        })}
      </View>
    );
  };

  const currentStep = WELCOME_STEPS_DATA[currentPage];
  const isLastStep = currentPage === WELCOME_STEPS_DATA.length - 1;

  return (
    <SafeAreaView style={styles.safeArea}>
      {renderProgressBar()}
      <PagerView
        ref={pagerRef}
        style={styles.pagerView}
        initialPage={0}
        onPageSelected={onPageSelected}
      >
        {WELCOME_STEPS_DATA.map((step, index) => (
          <View key={step.id} style={styles.page}>
            <WelcomeStep
              imageSource={step.imageSource}
              onGetStarted={!isLastStep ? handleGetStarted : undefined}
              onLogin={isLastStep ? handleLogin : undefined}
              onRegister={isLastStep ? handleRegister : undefined}
              showDualButtons={isLastStep}
            />
          </View>
        ))}
      </PagerView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Match asset background color (pure white)
  },
  progressBarContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 20,
    gap: 8,
    maxWidth: 300,
    alignSelf: 'center',
  },
  progressSegmentContainer: {
    width: 80,
    height: 4,
    position: 'relative',
  },
  progressSegmentBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
  },
  progressSegmentFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    backgroundColor: '#90EE90',
    borderRadius: 2,
  },
  pagerView: {
    flex: 1,
  },
  page: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF', // Match asset background color (pure white)
  },
});
