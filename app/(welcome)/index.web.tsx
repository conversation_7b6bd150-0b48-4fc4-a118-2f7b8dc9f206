import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import WelcomeStep from '../../components/welcome/WelcomeStep';
import { StyledTouchableOpacity, StyledText } from '../../components/ui/StyledComponents';

// Welcome steps data without text
const WELCOME_STEPS_DATA = [
  {
    id: '1',
    imageSource: require('../../assets/images/welcome-step-1.png'),
  },
  {
    id: '2',
    imageSource: require('../../assets/images/welcome-step-2.png'),
  },
  {
    id: '3',
    imageSource: require('../../assets/images/welcome-step-3.png'),
  },
];

export default function WelcomeIndexWeb() { 
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(0);
  const [progressWidth, setProgressWidth] = useState(0);
  const autoAdvanceTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const progressTimer = useRef<ReturnType<typeof setInterval> | null>(null);

  const handleGetStarted = async () => {
    // If not on the last step, advance to next page
    if (currentPage < WELCOME_STEPS_DATA.length - 1) {
      const nextPage = currentPage + 1;
      goToPage(nextPage);
      return;
    }
    
    // If on the last step, this shouldn't be called (but handle it just in case)
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleLogin = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/login');
    }
  };

  const handleRegister = async () => {
    try {
      await AsyncStorage.setItem('hasSeenWelcome', 'true');
      router.replace('/(auth)/register');
    } catch (error) {
      console.error('Failed to save welcome flag:', error);
      router.replace('/(auth)/register');
    }
  };

  const goToPage = (pageIndex: number) => {
    setCurrentPage(pageIndex);
    startProgressAnimation(pageIndex);
  };

  const startProgressAnimation = (pageIndex: number) => {
    // Clear existing timers
    if (autoAdvanceTimer.current) {
      clearTimeout(autoAdvanceTimer.current);
    }
    if (progressTimer.current) {
      clearInterval(progressTimer.current);
    }

    // Reset progress
    setProgressWidth(0);

    // Don't auto-advance on the last page
    if (pageIndex >= WELCOME_STEPS_DATA.length - 1) {
      setProgressWidth(100);
      return;
    }

    // Animate progress bar over 3 seconds (update every 50ms)
    let progress = 0;
    progressTimer.current = setInterval(() => {
      progress += (100 / 60); // 60 updates over 3 seconds
      if (progress >= 100) {
        progress = 100;
        if (progressTimer.current) {
          clearInterval(progressTimer.current);
        }
      }
      setProgressWidth(progress);
    }, 50);

    // Auto-advance to next page after 3 seconds
    autoAdvanceTimer.current = setTimeout(() => {
      const nextPage = pageIndex + 1;
      if (nextPage < WELCOME_STEPS_DATA.length) {
        goToPage(nextPage);
      }
    }, 3000);
  };

  useEffect(() => {
    // Start the first page animation
    startProgressAnimation(0);

    return () => {
      if (autoAdvanceTimer.current) {
        clearTimeout(autoAdvanceTimer.current);
      }
      if (progressTimer.current) {
        clearInterval(progressTimer.current);
      }
    };
  }, []);

  const renderProgressBar = () => {
    return (
      <View style={styles.progressBarContainer}>
        {WELCOME_STEPS_DATA.map((_, index) => {
          let segmentProgress = 0;
          
          if (index < currentPage) {
            // Completed segments
            segmentProgress = 100;
          } else if (index === currentPage) {
            // Current segment with animation
            segmentProgress = progressWidth;
          } else {
            // Future segments
            segmentProgress = 0;
          }

          return (
            <View key={index} style={styles.progressSegmentContainer}>
              <View style={styles.progressSegmentBackground} />
              <View
                style={[
                  styles.progressSegmentFill,
                  { width: `${segmentProgress}%` },
                ]}
              />
            </View>
          );
        })}
      </View>
    );
  };

  const currentStep = WELCOME_STEPS_DATA[currentPage];
  const isLastStep = currentPage === WELCOME_STEPS_DATA.length - 1;

  return (
    <View style={styles.outerContainer}>
      <View style={styles.container}>
        {renderProgressBar()}
        <View style={styles.contentContainer}>
          <WelcomeStep
            imageSource={currentStep.imageSource}
            onGetStarted={!isLastStep ? handleGetStarted : undefined}
            onLogin={isLastStep ? handleLogin : undefined}
            onRegister={isLastStep ? handleRegister : undefined}
            showDualButtons={isLastStep}
          />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF', // Match asset background color (pure white)
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh' as any, // Web-specific CSS
  },
  container: {
    width: '100%' as any,
    maxWidth: 780,
    backgroundColor: '#FFFFFF', // Match asset background color (pure white)
    alignSelf: 'center',
    paddingBottom: 40, // Add bottom padding to ensure button is visible
  },
  progressBarContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 20,
    gap: 8,
    maxWidth: 300,
    alignSelf: 'center',
  },
  progressSegmentContainer: {
    width: 80,
    height: 4,
    position: 'relative',
  },
  progressSegmentBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
  },
  progressSegmentFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    backgroundColor: '#90EE90',
    borderRadius: 2,
  },
  contentContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    minHeight: 600, // Ensure minimum height for content
  },
});
